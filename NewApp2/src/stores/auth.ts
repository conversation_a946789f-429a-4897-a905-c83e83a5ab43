import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import router from '@/router';
import apiClient from '@/api';

interface User {
  id: number;
  username: string;
  email: string;
  nickname?: string;
  avatar_url?: string;
  role: 'admin' | 'user';
  credits_balance?: number;
  created_at: string;
}

export const useAuthStore = defineStore('auth', () => {
  const token = ref(localStorage.getItem('token'));
  const user = ref<User | null>(null);

  const isAuthenticated = computed(() => !!token.value);

  function setToken(newToken: string) {
    token.value = newToken;
    localStorage.setItem('token', newToken);
  }

  function setUser(newUser: User) {
    user.value = newUser;
  }

  function clearAuth() {
    token.value = null;
    user.value = null;
    localStorage.removeItem('token');
  }

  async function login(credentials: {identifier: string, password: string}) {
    try {
      // Try test account first
      if (credentials.identifier === 'admin' && credentials.password === 'admin123') {
        const mockToken = 'mock-jwt-token-' + Date.now();
        const mockUser: User = {
          id: 1,
          username: 'admin',
          nickname: '管理员',
          email: '<EMAIL>',
          role: 'admin',
          avatar_url: '',
          created_at: new Date().toISOString(),
          credits_balance: 1000
        };

        setToken(mockToken);
        setUser(mockUser);
        router.push('/');
        return;
      }

      // Try real API
      const { data } = await apiClient.post('/auth/login', credentials);

      if (data.message === '登录成功' && data.user && data.token) {
        setToken(data.token);
        setUser(data.user);
        router.push('/');
      } else {
        throw new Error(data.message || '登录失败');
      }
    } catch (error: any) {
      console.error('Login error:', error);
      throw error;
    }
  }

  async function fetchUser() {
    if (!token.value) return;
    try {
      const { data } = await apiClient.get('/auth/me'); 
      setUser(data.user);
    } catch (error: any) {
      console.error('Failed to fetch user:', error);
      // Only clear auth if it's a 401 error, which is handled by the interceptor.
      // For other errors, we just log it but don't kick the user out.
      // The interceptor will catch 401s and redirect automatically.
    }
  }

  async function fetchCredits() {
    if (!token.value) return;
    try {
      const { data } = await apiClient.get('/credits/balance');
      if (user.value && data.success) {
        user.value.credits_balance = data.cumulativeCredits;
      }
    } catch (error) {
      console.error('Failed to fetch credits:', error);
    }
  }

  function logout() {
    clearAuth();
    router.push('/login');
  }

  // On store initialization, if a token exists, try to fetch the user.
  // This makes the session persistent across page reloads.
  if (token.value) {
    fetchUser();
  }

  return {
    token,
    user,
    isAuthenticated,
    login,
    logout,
    fetchUser,
    fetchCredits,
    setUser,
    setToken
  };
}); 