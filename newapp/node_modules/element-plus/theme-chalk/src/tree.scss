@use 'mixins/mixins' as *;
@use 'mixins/var' as *;
@use 'common/var' as *;
@use 'common/transition';

@include b(tree) {
  @include set-component-css-var('tree', $tree);
}

@include b(tree) {
  position: relative;
  cursor: default;
  background: getCssVar('fill-color', 'blank');
  color: getCssVar('tree-text-color');
  font-size: getCssVar('font-size', 'base');

  @include e(empty-block) {
    position: relative;
    min-height: 60px;
    text-align: center;
    width: 100%;
    height: 100%;
  }

  @include e(empty-text) {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    color: getCssVar('text-color', 'secondary');
    font-size: getCssVar('font-size', 'base');
  }

  @include e(drop-indicator) {
    position: absolute;
    left: 0;
    right: 0;
    height: 1px;
    background-color: getCssVar('color-primary');
  }
}

@include b(tree-node) {
  white-space: nowrap;
  outline: none;
  &:focus {
    /* focus */
    > .#{$namespace}-tree-node__content {
      background-color: getCssVar('tree-node-hover-bg-color');
    }
  }

  @include when(drop-inner) {
    > .#{$namespace}-tree-node__content .#{$namespace}-tree-node__label {
      background-color: getCssVar('color-primary');
      color: #fff;
    }
  }

  @include e(content) {
    @include css-var-from-global('checkbox-height', 'tree-node-content-height');

    display: flex;
    align-items: center;
    height: getCssVar('tree-node-content-height');
    cursor: pointer;

    & > .#{$namespace}-tree-node__expand-icon {
      padding: 6px;
      box-sizing: content-box;
    }
    & > label.#{$namespace}-checkbox {
      margin-right: 8px;
    }
    &:hover {
      background-color: getCssVar('tree-node-hover-bg-color');
    }

    .#{$namespace}-tree.is-dragging & {
      cursor: move;

      & * {
        pointer-events: none;
      }
    }

    .#{$namespace}-tree.is-dragging.is-drop-not-allow & {
      cursor: not-allowed;
    }
  }

  @include e(expand-icon) {
    cursor: pointer;
    color: getCssVar('tree-expand-icon-color');
    font-size: 12px;

    transform: rotate(0deg);
    transition: transform getCssVar('transition-duration') ease-in-out;

    &.expanded {
      transform: rotate(90deg);
    }

    &.is-leaf {
      color: transparent;
      cursor: default;
      visibility: hidden;
    }
    &.is-hidden {
      visibility: hidden;
    }
  }

  @include e(loading-icon) {
    margin-right: 8px;
    font-size: getCssVar('font-size', 'base');
    color: getCssVar('tree-expand-icon-color');
  }

  & > .#{$namespace}-tree-node__children {
    overflow: hidden;
    background-color: transparent;
  }

  &.is-expanded > .#{$namespace}-tree-node__children {
    display: block;
  }
}

.#{$namespace}-tree--highlight-current
  .#{$namespace}-tree-node.is-current
  > .#{$namespace}-tree-node__content {
  background-color: getCssVar('color-primary-light-9');
}
