@use '../common/var' as *;
@use '../mixins/mixins' as *;
@use '../mixins/var' as *;
@use './picker-panel.scss';

@include b(date-picker) {
  @include set-component-css-var('datepicker', $datepicker);
}

@include b(date-picker) {
  width: 322px;

  &.has-sidebar.has-time {
    width: 434px;
  }

  &.has-sidebar {
    width: 438px;
  }

  &.has-time .#{$namespace}-picker-panel__body-wrapper {
    position: relative;
  }

  .#{$namespace}-picker-panel__content {
    width: 292px;
  }

  table {
    table-layout: fixed;
    width: 100%;
  }

  @include e(editor-wrap) {
    position: relative;
    display: table-cell;
    padding: 0 5px;
  }

  @include e(time-header) {
    position: relative;
    border-bottom: 1px solid getCssVar('datepicker-inner-border-color');
    font-size: 12px;
    padding: 8px 5px 5px;
    display: table;
    width: 100%;
    box-sizing: border-box;
  }

  @include e(header) {
    padding: 12px 12px 0;
    text-align: center;

    @include m(bordered) {
      margin-bottom: 0;
      padding-bottom: 12px;
      border-bottom: solid 1px getCssVar('border-color-lighter');

      & + .#{$namespace}-picker-panel__content {
        margin-top: 0;
      }
    }
  }

  @include e(header-label) {
    font-size: 16px;
    font-weight: 500;
    padding: 0 5px;
    line-height: 22px;
    text-align: center;
    cursor: pointer;
    color: getCssVar('text-color', 'regular');

    &:hover {
      color: getCssVar('datepicker-hover-text-color');
    }
    &:focus-visible {
      outline: none;
      color: getCssVar('datepicker-hover-text-color');
    }

    &.active {
      color: getCssVar('datepicker-active-color');
    }
  }

  @include e(prev-btn) {
    float: left;
  }

  @include e(next-btn) {
    float: right;
  }

  @include e(time-wrap) {
    padding: 10px;
    text-align: center;
  }

  @include e(time-label) {
    float: left;
    cursor: pointer;
    line-height: 30px;
    margin-left: 10px;
  }

  .#{$namespace}-time-panel {
    position: absolute;
  }
}
