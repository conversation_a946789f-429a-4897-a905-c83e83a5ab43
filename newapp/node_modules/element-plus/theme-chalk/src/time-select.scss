@use 'mixins/config' as *;
@use 'mixins/mixins' as *;
@use 'common/var' as *;
@use './date-picker/picker.scss';
@use './date-picker/date-picker.scss';

.time-select {
  margin: 5px 0;
  min-width: 0;

  .#{$namespace}-picker-panel__content {
    max-height: 200px;
    margin: 0;
  }

  &-item {
    padding: 8px 10px;
    font-size: 14px;
    line-height: 20px;

    &.disabled {
      color: getCssVar('datepicker-border-color');
      cursor: not-allowed;
    }

    &:hover {
      background-color: getCssVar('fill-color', 'light');
      font-weight: bold;
      cursor: pointer;
    }
  }

  .time-select-item.selected:not(.disabled) {
    color: getCssVar('color-primary');
    font-weight: bold;
  }
}
