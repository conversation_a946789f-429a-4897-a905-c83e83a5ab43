import type { ExtractPropTypes, __ExtractPublicPropTypes } from 'vue';
import type Pager from './pager.vue';
export declare const paginationPagerProps: {
    readonly currentPage: import("element-plus/es/utils").EpPropFinalized<NumberConstructor, unknown, unknown, 1, boolean>;
    readonly pageCount: {
        readonly type: import("vue").PropType<number>;
        readonly required: true;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    readonly pagerCount: import("element-plus/es/utils").EpPropFinalized<NumberConstructor, unknown, unknown, 7, boolean>;
    readonly disabled: BooleanConstructor;
};
export type PaginationPagerProps = ExtractPropTypes<typeof paginationPagerProps>;
export type PaginationPagerPropsPublic = __ExtractPublicPropTypes<typeof paginationPagerProps>;
export type PagerInstance = InstanceType<typeof Pager> & unknown;
