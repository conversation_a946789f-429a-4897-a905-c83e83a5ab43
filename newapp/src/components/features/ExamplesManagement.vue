<template>
  <div class="examples-management">
    <!-- Search and Filters -->
    <el-card class="filter-card" shadow="never">
      <template #header>
        <div class="card-header">
          <span class="card-title">案例管理</span>
          <el-button type="primary" @click="showAddDialog = true">
            <el-icon><Plus /></el-icon>
            添加案例
          </el-button>
        </div>
      </template>

      <!-- Search Bar -->
      <div class="search-section">
        <el-input
          v-model="searchQuery"
          placeholder="搜索标题、提示词或标签..."
          size="large"
          clearable
          @input="handleSearch"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>

      <!-- Filters -->
      <div class="filters-section">
        <el-row :gutter="16">
          <el-col :span="6">
            <el-select
              v-model="selectedCategory"
              placeholder="选择分类"
              clearable
              @change="handleFilterChange"
              style="width: 100%"
            >
              <el-option
                v-for="category in categories"
                :key="category.id"
                :label="category.name"
                :value="category.slug"
              />
            </el-select>
          </el-col>

          <el-col :span="6">
            <el-select
              v-model="selectedModel"
              placeholder="选择模型"
              clearable
              @change="handleFilterChange"
              style="width: 100%"
            >
              <el-option label="MJ" value="MJ" />
              <el-option label="FLUX" value="FLUX" />
              <el-option label="FLUX-多图" value="FLUX-多图" />
              <el-option label="SDXL" value="SDXL" />
              <el-option label="超级生图" value="超级生图" />
              <el-option label="高级生图" value="高级生图" />
              <el-option label="GPT4O" value="GPT4O" />
              <el-option label="GPT4O-编辑" value="GPT4O-编辑" />
              <el-option label="其他" value="其他" />
            </el-select>
          </el-col>

          <el-col :span="6">
            <el-select
              v-model="sortBy"
              placeholder="排序方式"
              @change="handleFilterChange"
              style="width: 100%"
            >
              <el-option label="最新发布" value="date_desc" />
              <el-option label="最多点赞" value="likes_desc" />
              <el-option label="最早发布" value="date_asc" />
            </el-select>
          </el-col>

          <el-col :span="6">
            <el-button @click="resetFilters" style="width: 100%">
              <el-icon><Refresh /></el-icon>
              重置筛选
            </el-button>
          </el-col>
        </el-row>
      </div>
    </el-card>

    <!-- Examples Grid -->
    <div class="examples-container">
      <!-- Loading State -->
      <div v-if="isLoading" class="flex items-center justify-center py-12">
        <div class="text-center">
          <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <span class="text-gray-400">加载案例中...</span>
        </div>
      </div>

      <!-- Empty State -->
      <div v-else-if="false" class="text-center py-12">
        <svg class="mx-auto h-12 w-12 text-gray-500 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
        <span class="text-gray-400">没有找到符合条件的案例。</span>
      </div>

      <!-- Examples Grid -->
      <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        <!-- Mock Example Cards -->
        <div
          v-for="i in 8"
          :key="i"
          class="card-glass rounded-lg overflow-hidden hover:scale-105 transition-transform duration-200"
        >
          <div class="aspect-square bg-gray-800 flex items-center justify-center">
            <span class="text-gray-400">示例图片 {{ i }}</span>
          </div>
          <div class="p-4">
            <h3 class="text-white font-medium mb-2">示例案例 {{ i }}</h3>
            <p class="text-gray-400 text-sm mb-3">这是一个示例案例的描述...</p>
            <div class="flex items-center justify-between text-xs text-gray-500">
              <span>2小时前</span>
              <span>❤️ {{ i * 3 }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Load More -->
      <div class="text-center mt-8">
        <button
          @click="loadMore"
          :disabled="isLoading"
          class="btn-primary disabled:opacity-50"
        >
          <span v-if="isLoading" class="flex items-center">
            <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
            加载中...
          </span>
          <span v-else>加载更多</span>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Search, Refresh } from '@element-plus/icons-vue'
import ExampleCard from '../ui/ExampleCard.vue'
import AddExampleDialog from './AddExampleDialog.vue'

// Reactive data
const searchQuery = ref('')
const selectedCategory = ref('')
const selectedModel = ref('')
const sortBy = ref('date_desc')
const isLoading = ref(false)
const showAddDialog = ref(false)
const currentPage = ref(1)
const pageSize = ref(24)
const total = ref(0)

// Mock data
const categories = ref([
  { id: 1, name: '人物', slug: 'people' },
  { id: 2, name: '风景', slug: 'landscape' },
  { id: 3, name: '动物', slug: 'animals' },
  { id: 4, name: '建筑', slug: 'architecture' },
  { id: 5, name: '艺术', slug: 'art' },
])

const examples = ref([
  {
    id: 1,
    title: '美丽的风景画',
    prompt: 'A beautiful landscape with mountains and lakes',
    image_url: 'https://picsum.photos/400/400?random=1',
    model: 'MJ',
    category: { name: '风景', slug: 'landscape' },
    tags: [{ id: 1, name: '自然' }, { id: 2, name: '山水' }],
    likes_count: 15,
    is_liked: false,
    created_at: new Date().toISOString(),
    width: 1024,
    height: 1024,
    user_id: 1
  },
  {
    id: 2,
    title: '科幻人物设计',
    prompt: 'Futuristic character design with cyberpunk elements',
    image_url: 'https://picsum.photos/400/400?random=2',
    model: 'FLUX',
    category: { name: '人物', slug: 'people' },
    tags: [{ id: 3, name: '科幻' }, { id: 4, name: '人物' }],
    likes_count: 23,
    is_liked: true,
    created_at: new Date().toISOString(),
    width: 1024,
    height: 1024,
    user_id: 1
  },
  // Add more mock examples...
])

// Computed
const filteredExamples = computed(() => {
  let filtered = examples.value

  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(example =>
      example.title.toLowerCase().includes(query) ||
      example.prompt.toLowerCase().includes(query)
    )
  }

  if (selectedCategory.value) {
    filtered = filtered.filter(example =>
      example.category?.slug === selectedCategory.value
    )
  }

  if (selectedModel.value) {
    filtered = filtered.filter(example =>
      example.model === selectedModel.value
    )
  }

  return filtered
})

// Methods
const handleSearch = () => {
  currentPage.value = 1
  loadExamples()
}

const handleFilterChange = () => {
  currentPage.value = 1
  loadExamples()
}

const resetFilters = () => {
  searchQuery.value = ''
  selectedCategory.value = ''
  selectedModel.value = ''
  sortBy.value = 'date_desc'
  currentPage.value = 1
  loadExamples()
}

const loadExamples = async () => {
  isLoading.value = true
  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000))
    total.value = filteredExamples.value.length
  } catch (error) {
    ElMessage.error('加载案例失败')
  } finally {
    isLoading.value = false
  }
}

const handleLike = async (exampleId: number) => {
  const example = examples.value.find(e => e.id === exampleId)
  if (example) {
    example.is_liked = !example.is_liked
    example.likes_count += example.is_liked ? 1 : -1
    ElMessage.success(example.is_liked ? '点赞成功' : '取消点赞')
  }
}

const handleDelete = async (exampleId: number) => {
  try {
    await ElMessageBox.confirm('确定要删除这个案例吗？', '确认删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })

    const index = examples.value.findIndex(e => e.id === exampleId)
    if (index > -1) {
      examples.value.splice(index, 1)
      ElMessage.success('删除成功')
    }
  } catch {
    // User cancelled
  }
}

const handleView = (example: any) => {
  // TODO: Show example detail modal
  console.log('View example:', example)
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  loadExamples()
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  loadExamples()
}

const handleAddSuccess = () => {
  showAddDialog.value = false
  loadExamples()
  ElMessage.success('添加案例成功')
}

onMounted(() => {
  loadExamples()
})
</script>

<style scoped>
.examples-management {
  padding: 20px;
}

.filter-card {
  margin-bottom: 20px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 1.2rem;
  font-weight: bold;
  color: #e4e7ed;
}

.search-section {
  margin-bottom: 20px;
}

.filters-section {
  margin-bottom: 10px;
}

.examples-grid {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  padding: 20px;
}

.loading-container {
  padding: 40px;
}

.examples-list {
  min-height: 400px;
}

.example-col {
  margin-bottom: 20px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* Element Plus dark theme customization */
:deep(.el-card) {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

:deep(.el-input__wrapper) {
  background-color: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

:deep(.el-input__inner) {
  color: #e4e7ed;
}

:deep(.el-select .el-input__wrapper) {
  background-color: rgba(255, 255, 255, 0.1);
}

:deep(.el-pagination) {
  color: #e4e7ed;
}

:deep(.el-pagination .el-pager li) {
  background-color: rgba(255, 255, 255, 0.1);
  color: #e4e7ed;
}

:deep(.el-pagination .el-pager li.is-active) {
  background-color: #409eff;
  color: white;
}
</style>
