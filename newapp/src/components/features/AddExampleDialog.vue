<template>
  <el-dialog
    v-model="dialogVisible"
    title="添加案例"
    width="600px"
    :before-close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="80px"
      label-position="top"
    >
      <el-form-item label="标题" prop="title">
        <el-input
          v-model="form.title"
          placeholder="请输入案例标题"
          maxlength="100"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="提示词" prop="prompt">
        <el-input
          v-model="form.prompt"
          type="textarea"
          :rows="4"
          placeholder="请输入生成图片的提示词"
          maxlength="1000"
          show-word-limit
        />
      </el-form-item>

      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="分类" prop="category">
            <el-select
              v-model="form.category"
              placeholder="选择分类"
              style="width: 100%"
            >
              <el-option
                v-for="category in categories"
                :key="category.id"
                :label="category.name"
                :value="category.slug"
              />
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="模型" prop="model">
            <el-select
              v-model="form.model"
              placeholder="选择模型"
              style="width: 100%"
            >
              <el-option label="MJ" value="MJ" />
              <el-option label="FLUX" value="FLUX" />
              <el-option label="FLUX-多图" value="FLUX-多图" />
              <el-option label="SDXL" value="SDXL" />
              <el-option label="超级生图" value="超级生图" />
              <el-option label="高级生图" value="高级生图" />
              <el-option label="GPT4O" value="GPT4O" />
              <el-option label="GPT4O-编辑" value="GPT4O-编辑" />
              <el-option label="其他" value="其他" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="标签">
        <el-select
          v-model="form.tags"
          multiple
          filterable
          allow-create
          placeholder="选择或创建标签"
          style="width: 100%"
        >
          <el-option
            v-for="tag in availableTags"
            :key="tag.id"
            :label="tag.name"
            :value="tag.name"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="图片" prop="image">
        <el-upload
          ref="uploadRef"
          class="image-uploader"
          :show-file-list="false"
          :before-upload="beforeUpload"
          :on-success="handleUploadSuccess"
          :on-error="handleUploadError"
          action="#"
          :auto-upload="false"
        >
          <el-image
            v-if="form.imageUrl"
            :src="form.imageUrl"
            class="uploaded-image"
            fit="cover"
          />
          <div v-else class="upload-placeholder">
            <el-icon class="upload-icon"><Plus /></el-icon>
            <div class="upload-text">点击上传图片</div>
            <div class="upload-hint">支持 JPG、PNG 格式，大小不超过 10MB</div>
          </div>
        </el-upload>
      </el-form-item>

      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="宽度" prop="width">
            <el-input-number
              v-model="form.width"
              :min="1"
              :max="4096"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="高度" prop="height">
            <el-input-number
              v-model="form.height"
              :min="1"
              :max="4096"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" :loading="submitting" @click="handleSubmit">
          {{ submitting ? '提交中...' : '确定' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'

interface Props {
  modelValue: boolean
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  success: []
}>()

const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const formRef = ref<FormInstance>()
const uploadRef = ref()
const submitting = ref(false)

const form = reactive({
  title: '',
  prompt: '',
  category: '',
  model: '',
  tags: [] as string[],
  imageUrl: '',
  imageFile: null as File | null,
  width: 1024,
  height: 1024
})

const categories = ref([
  { id: 1, name: '人物', slug: 'people' },
  { id: 2, name: '风景', slug: 'landscape' },
  { id: 3, name: '动物', slug: 'animals' },
  { id: 4, name: '建筑', slug: 'architecture' },
  { id: 5, name: '艺术', slug: 'art' },
])

const availableTags = ref([
  { id: 1, name: '自然' },
  { id: 2, name: '人物' },
  { id: 3, name: '科幻' },
  { id: 4, name: '艺术' },
  { id: 5, name: '风景' },
])

const rules: FormRules = {
  title: [
    { required: true, message: '请输入标题', trigger: 'blur' },
    { min: 2, max: 100, message: '标题长度在 2 到 100 个字符', trigger: 'blur' }
  ],
  prompt: [
    { required: true, message: '请输入提示词', trigger: 'blur' },
    { min: 10, max: 1000, message: '提示词长度在 10 到 1000 个字符', trigger: 'blur' }
  ],
  category: [
    { required: true, message: '请选择分类', trigger: 'change' }
  ],
  model: [
    { required: true, message: '请选择模型', trigger: 'change' }
  ],
  image: [
    { required: true, message: '请上传图片', trigger: 'change' }
  ],
  width: [
    { required: true, message: '请输入宽度', trigger: 'blur' }
  ],
  height: [
    { required: true, message: '请输入高度', trigger: 'blur' }
  ]
}

const beforeUpload = (file: File) => {
  const isImage = file.type.startsWith('image/')
  const isLt10M = file.size / 1024 / 1024 < 10

  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }
  if (!isLt10M) {
    ElMessage.error('图片大小不能超过 10MB!')
    return false
  }

  // Create preview URL
  const reader = new FileReader()
  reader.onload = (e) => {
    form.imageUrl = e.target?.result as string
  }
  reader.readAsDataURL(file)
  
  form.imageFile = file
  return false // Prevent auto upload
}

const handleUploadSuccess = () => {
  ElMessage.success('图片上传成功')
}

const handleUploadError = () => {
  ElMessage.error('图片上传失败')
}

const resetForm = () => {
  Object.assign(form, {
    title: '',
    prompt: '',
    category: '',
    model: '',
    tags: [],
    imageUrl: '',
    imageFile: null,
    width: 1024,
    height: 1024
  })
  formRef.value?.resetFields()
}

const handleClose = () => {
  resetForm()
  emit('update:modelValue', false)
}

const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    
    if (!form.imageFile) {
      ElMessage.error('请上传图片')
      return
    }

    submitting.value = true

    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 2000))

    ElMessage.success('添加案例成功')
    emit('success')
    handleClose()
  } catch (error) {
    console.error('Validation failed:', error)
  } finally {
    submitting.value = false
  }
}

// Watch for dialog visibility changes
watch(() => props.modelValue, (newVal) => {
  if (!newVal) {
    resetForm()
  }
})
</script>

<style scoped>
.image-uploader {
  width: 100%;
}

.uploaded-image {
  width: 200px;
  height: 200px;
  border-radius: 8px;
  border: 1px solid #dcdfe6;
}

.upload-placeholder {
  width: 200px;
  height: 200px;
  border: 2px dashed #dcdfe6;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: border-color 0.3s;
}

.upload-placeholder:hover {
  border-color: #409eff;
}

.upload-icon {
  font-size: 28px;
  color: #8c939d;
  margin-bottom: 8px;
}

.upload-text {
  color: #606266;
  font-size: 14px;
  margin-bottom: 4px;
}

.upload-hint {
  color: #909399;
  font-size: 12px;
}

.dialog-footer {
  text-align: right;
}

/* Element Plus dark theme customization */
:deep(.el-dialog) {
  background: rgba(30, 30, 46, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

:deep(.el-dialog__header) {
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

:deep(.el-dialog__title) {
  color: #e4e7ed;
}

:deep(.el-form-item__label) {
  color: #e4e7ed;
}

:deep(.el-input__wrapper) {
  background-color: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

:deep(.el-textarea__inner) {
  background-color: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: #e4e7ed;
}

:deep(.el-select .el-input__wrapper) {
  background-color: rgba(255, 255, 255, 0.1);
}
</style>
