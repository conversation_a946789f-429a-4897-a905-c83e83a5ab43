<template>
  <div class="ai-generation">
    <el-row :gutter="20">
      <!-- Left Panel - Prompt and Settings -->
      <el-col :span="14">
        <el-card class="prompt-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span class="card-title">AI 图片生成</span>
              <el-button type="primary" @click="showTemplates = true">
                <el-icon><Collection /></el-icon>
                提示词模板
              </el-button>
            </div>
          </template>

          <!-- Prompt Input -->
          <div class="prompt-section">
            <el-form-item label="提示词 (Prompt)">
              <el-input
                v-model="form.prompt"
                type="textarea"
                :rows="4"
                placeholder="描述你想要生成的图片..."
                maxlength="2000"
                show-word-limit
              />
            </el-form-item>

            <el-form-item label="负面提示词 (Negative Prompt)">
              <el-input
                v-model="form.negativePrompt"
                type="textarea"
                :rows="2"
                placeholder="描述你不想要的元素..."
                maxlength="1000"
                show-word-limit
              />
            </el-form-item>
          </div>

          <!-- Model and Settings -->
          <div class="settings-section">
            <el-row :gutter="16">
              <el-col :span="12">
                <el-form-item label="生成模型">
                  <el-select
                    v-model="form.model"
                    placeholder="选择生成模型"
                    style="width: 100%"
                  >
                    <el-option label="FLUX Pro" value="flux-pro">
                      <span>FLUX Pro</span>
                      <span style="float: right; color: #8cc8ff; font-size: 13px">推荐</span>
                    </el-option>
                    <el-option label="FLUX Dev" value="flux-dev" />
                    <el-option label="Midjourney" value="midjourney" />
                    <el-option label="SDXL" value="sdxl" />
                    <el-option label="SD 3.0" value="sd3" />
                  </el-select>
                </el-form-item>
              </el-col>

              <el-col :span="12">
                <el-form-item label="图片尺寸">
                  <el-select
                    v-model="form.aspectRatio"
                    placeholder="选择尺寸比例"
                    style="width: 100%"
                  >
                    <el-option label="1:1 (正方形)" value="1:1" />
                    <el-option label="16:9 (横屏)" value="16:9" />
                    <el-option label="9:16 (竖屏)" value="9:16" />
                    <el-option label="4:3 (标准)" value="4:3" />
                    <el-option label="3:4 (竖版)" value="3:4" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="16">
              <el-col :span="8">
                <el-form-item label="生成数量">
                  <el-input-number
                    v-model="form.count"
                    :min="1"
                    :max="4"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>

              <el-col :span="8">
                <el-form-item label="引导强度">
                  <el-slider
                    v-model="form.guidance"
                    :min="1"
                    :max="20"
                    :step="0.5"
                    show-input
                  />
                </el-form-item>
              </el-col>

              <el-col :span="8">
                <el-form-item label="推理步数">
                  <el-slider
                    v-model="form.steps"
                    :min="10"
                    :max="50"
                    :step="5"
                    show-input
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <!-- Advanced Settings -->
            <el-collapse v-model="activeCollapse">
              <el-collapse-item title="高级设置" name="advanced">
                <el-row :gutter="16">
                  <el-col :span="12">
                    <el-form-item label="随机种子">
                      <el-input
                        v-model="form.seed"
                        placeholder="留空随机生成"
                        type="number"
                      />
                    </el-form-item>
                  </el-col>

                  <el-col :span="12">
                    <el-form-item label="风格预设">
                      <el-select
                        v-model="form.style"
                        placeholder="选择风格"
                        style="width: 100%"
                        clearable
                      >
                        <el-option label="摄影风格" value="photography" />
                        <el-option label="艺术绘画" value="artistic" />
                        <el-option label="动漫风格" value="anime" />
                        <el-option label="概念艺术" value="concept" />
                        <el-option label="写实风格" value="realistic" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-collapse-item>
            </el-collapse>
          </div>

          <!-- Generate Button -->
          <div class="generate-section">
            <el-button
              type="primary"
              size="large"
              :loading="isGenerating"
              :disabled="!canGenerate"
              @click="generateImages"
              style="width: 100%"
            >
              <template #loading>
                <el-icon class="is-loading"><Loading /></el-icon>
              </template>
              <el-icon v-if="!isGenerating"><MagicStick /></el-icon>
              {{ isGenerating ? '正在生成图片...' : '开始生成' }}
            </el-button>
          </div>
        </el-card>
      </el-col>

      <!-- Right Panel - Results -->
      <el-col :span="10">
        <el-card class="results-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span class="card-title">生成结果</span>
              <el-button
                v-if="generatedImages.length > 0"
                type="success"
                size="small"
                @click="downloadAll"
              >
                <el-icon><Download /></el-icon>
                下载全部
              </el-button>
            </div>
          </template>

          <!-- Loading State -->
          <div v-if="isGenerating" class="loading-container">
            <el-result icon="loading" title="AI 正在创作中">
              <template #sub-title>
                <p>使用 {{ form.model }} 模型生成 {{ form.count }} 张图片</p>
                <el-progress :percentage="progress" :show-text="false" class="progress-bar" />
                <p class="progress-text">预计需要 {{ estimatedTime }} 秒</p>
              </template>
            </el-result>
          </div>

          <!-- Error State -->
          <el-alert
            v-else-if="error"
            :title="error"
            type="error"
            :closable="false"
            show-icon
          />

          <!-- Results Grid -->
          <div v-else-if="generatedImages.length > 0" class="results-grid">
            <div
              v-for="(image, index) in generatedImages"
              :key="index"
              class="image-item"
            >
              <el-image
                :src="image.url"
                :alt="`Generated image ${index + 1}`"
                fit="cover"
                class="generated-image"
                @click="previewImage(image)"
              >
                <template #error>
                  <div class="image-error">
                    <el-icon><Picture /></el-icon>
                    <span>加载失败</span>
                  </div>
                </template>
              </el-image>
              
              <div class="image-actions">
                <el-button-group>
                  <el-button size="small" @click="previewImage(image)">
                    <el-icon><View /></el-icon>
                  </el-button>
                  <el-button size="small" @click="downloadImage(image)">
                    <el-icon><Download /></el-icon>
                  </el-button>
                  <el-button size="small" @click="saveToExamples(image)">
                    <el-icon><Collection /></el-icon>
                  </el-button>
                  <el-button size="small" type="danger" @click="deleteImage(index)">
                    <el-icon><Delete /></el-icon>
                  </el-button>
                </el-button-group>
              </div>
            </div>
          </div>

          <!-- Empty State -->
          <el-empty v-else description="输入提示词开始生成图片">
            <el-button type="primary" @click="focusPrompt">
              <el-icon><Edit /></el-icon>
              开始创作
            </el-button>
          </el-empty>
        </el-card>
      </el-col>
    </el-row>

    <!-- Image Preview Dialog -->
    <ImagePreviewDialog
      v-model="showPreview"
      :image="previewImageData"
      @save="saveToExamples"
      @download="downloadImage"
    />

    <!-- Prompt Templates Dialog -->
    <PromptTemplatesDialog
      v-model="showTemplates"
      @select="selectTemplate"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Collection,
  Loading,
  MagicStick,
  Download,
  Picture,
  View,
  Delete,
  Edit
} from '@element-plus/icons-vue'
import ImagePreviewDialog from '../ui/ImagePreviewDialog.vue'
import PromptTemplatesDialog from '../ui/PromptTemplatesDialog.vue'

// Reactive data
const form = reactive({
  prompt: '',
  negativePrompt: '',
  model: 'flux-pro',
  aspectRatio: '1:1',
  count: 1,
  guidance: 7.5,
  steps: 25,
  seed: '',
  style: ''
})

const isGenerating = ref(false)
const error = ref('')
const progress = ref(0)
const estimatedTime = ref(30)
const activeCollapse = ref<string[]>([])
const showPreview = ref(false)
const showTemplates = ref(false)
const previewImageData = ref<any>(null)

interface GeneratedImage {
  id: string
  url: string
  prompt: string
  model: string
  settings: any
  createdAt: string
}

const generatedImages = ref<GeneratedImage[]>([])

// Computed
const canGenerate = computed(() => {
  return form.prompt.trim().length > 0 && form.model
})

// Methods
const generateImages = async () => {
  if (!canGenerate.value) return

  isGenerating.value = true
  error.value = ''
  progress.value = 0
  estimatedTime.value = 30

  try {
    // Simulate progress
    const progressInterval = setInterval(() => {
      if (progress.value < 90) {
        progress.value += Math.random() * 15
        estimatedTime.value = Math.max(0, estimatedTime.value - 2)
      }
    }, 1000)

    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 8000))
    
    clearInterval(progressInterval)
    progress.value = 100

    // Mock generated images
    const mockImages: GeneratedImage[] = []
    for (let i = 0; i < form.count; i++) {
      mockImages.push({
        id: `img_${Date.now()}_${i}`,
        url: `https://picsum.photos/512/512?random=${Date.now() + i}`,
        prompt: form.prompt,
        model: form.model,
        settings: { ...form },
        createdAt: new Date().toISOString()
      })
    }

    generatedImages.value = [...mockImages, ...generatedImages.value]
    ElMessage.success(`成功生成 ${form.count} 张图片`)
  } catch (err) {
    error.value = '生成失败，请重试'
    ElMessage.error('生成失败，请重试')
  } finally {
    isGenerating.value = false
    progress.value = 0
  }
}

const previewImage = (image: GeneratedImage) => {
  previewImageData.value = image
  showPreview.value = true
}

const downloadImage = (image: GeneratedImage) => {
  const link = document.createElement('a')
  link.href = image.url
  link.download = `generated_${image.id}.png`
  link.click()
  ElMessage.success('下载成功')
}

const downloadAll = () => {
  generatedImages.value.forEach((image, index) => {
    setTimeout(() => {
      downloadImage(image)
    }, index * 500)
  })
}

const saveToExamples = async (image: GeneratedImage) => {
  try {
    await ElMessageBox.confirm('是否将此图片保存到案例库？', '保存确认', {
      confirmButtonText: '保存',
      cancelButtonText: '取消',
      type: 'info',
    })
    
    // TODO: Implement save to examples
    ElMessage.success('保存成功')
  } catch {
    // User cancelled
  }
}

const deleteImage = async (index: number) => {
  try {
    await ElMessageBox.confirm('确定要删除这张图片吗？', '删除确认', {
      confirmButtonText: '删除',
      cancelButtonText: '取消',
      type: 'warning',
    })
    
    generatedImages.value.splice(index, 1)
    ElMessage.success('删除成功')
  } catch {
    // User cancelled
  }
}

const selectTemplate = (template: any) => {
  form.prompt = template.prompt
  if (template.negativePrompt) {
    form.negativePrompt = template.negativePrompt
  }
  if (template.settings) {
    Object.assign(form, template.settings)
  }
  showTemplates.value = false
  ElMessage.success('模板已应用')
}

const focusPrompt = () => {
  // Focus on prompt textarea
  const textarea = document.querySelector('textarea')
  textarea?.focus()
}

onMounted(() => {
  // Load any saved settings or recent prompts
})
</script>

<style scoped>
.ai-generation {
  padding: 20px;
}

.prompt-card,
.results-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 1.2rem;
  font-weight: bold;
  color: #e4e7ed;
}

.prompt-section {
  margin-bottom: 20px;
}

.settings-section {
  margin-bottom: 20px;
}

.generate-section {
  margin-top: 20px;
}

.loading-container {
  padding: 40px 20px;
  text-align: center;
}

.progress-bar {
  margin: 16px 0;
  max-width: 300px;
  margin-left: auto;
  margin-right: auto;
}

.progress-text {
  color: #909399;
  font-size: 14px;
  margin-top: 8px;
}

.results-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
  min-height: 400px;
}

.image-item {
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.05);
  transition: transform 0.3s ease;
}

.image-item:hover {
  transform: translateY(-2px);
}

.generated-image {
  width: 100%;
  height: 200px;
  cursor: pointer;
}

.image-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #909399;
  background: rgba(255, 255, 255, 0.1);
}

.image-actions {
  position: absolute;
  bottom: 8px;
  left: 8px;
  right: 8px;
  opacity: 0;
  transition: opacity 0.3s ease;
  display: flex;
  justify-content: center;
}

.image-item:hover .image-actions {
  opacity: 1;
}

/* Element Plus dark theme customization */
:deep(.el-card) {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

:deep(.el-form-item__label) {
  color: #e4e7ed !important;
}

:deep(.el-input__wrapper) {
  background-color: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

:deep(.el-textarea__inner) {
  background-color: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: #e4e7ed;
}

:deep(.el-select .el-input__wrapper) {
  background-color: rgba(255, 255, 255, 0.1);
}

:deep(.el-slider__runway) {
  background-color: rgba(255, 255, 255, 0.2);
}

:deep(.el-collapse-item__header) {
  color: #e4e7ed;
  background-color: rgba(255, 255, 255, 0.05);
}

:deep(.el-collapse-item__content) {
  background-color: rgba(255, 255, 255, 0.05);
}

:deep(.el-button-group .el-button) {
  background-color: rgba(0, 0, 0, 0.5);
  border-color: rgba(255, 255, 255, 0.2);
  color: #e4e7ed;
}

:deep(.el-button-group .el-button:hover) {
  background-color: rgba(0, 0, 0, 0.7);
}
</style>
