<template>
  <div class="reverse-prompt">
    <el-row :gutter="20">
      <!-- Left Panel - Upload and Settings -->
      <el-col :span="12">
        <el-card class="upload-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span class="card-title">图片上传与设置</span>
            </div>
          </template>

          <!-- Image Upload Area -->
          <div class="upload-section">
            <el-upload
              ref="uploadRef"
              class="image-uploader"
              :show-file-list="false"
              :before-upload="beforeUpload"
              :on-success="handleUploadSuccess"
              :on-error="handleUploadError"
              action="#"
              :auto-upload="false"
              drag
            >
              <div v-if="!selectedImage" class="upload-placeholder">
                <el-icon class="upload-icon"><UploadFilled /></el-icon>
                <div class="upload-text">将图片拖拽到此处，或点击选择文件</div>
                <div class="upload-hint">支持 JPG、PNG 格式，大小不超过 10MB</div>
                <div class="upload-hint">支持粘贴上传 (Ctrl+V)</div>
              </div>

              <div v-else class="uploaded-image-container">
                <el-image
                  :src="selectedImage"
                  alt="Preview"
                  class="uploaded-image"
                  fit="contain"
                />
                <div class="image-actions">
                  <el-button type="danger" size="small" @click.stop="removeImage">
                    <el-icon><Delete /></el-icon>
                    移除图片
                  </el-button>
                </div>
              </div>
            </el-upload>
          </div>

          <!-- Settings -->
          <div class="settings-section">
            <!-- Model Selection -->
            <el-form-item label="选择AI模型">
              <el-select
                v-model="selectedModel"
                placeholder="选择分析模型"
                style="width: 100%"
              >
                <el-option label="GPT-4o" value="gpt-4o">
                  <span>GPT-4o</span>
                  <span style="float: right; color: #8cc8ff; font-size: 13px">推荐</span>
                </el-option>
                <el-option label="Claude" value="claude" />
                <el-option label="Gemini" value="gemini" />
              </el-select>
            </el-form-item>

            <!-- Prompt Types -->
            <el-form-item label="生成提示词类型">
              <el-checkbox-group v-model="promptTypes">
                <el-checkbox label="general">通用详细描述</el-checkbox>
                <el-checkbox label="mj">Midjourney 风格</el-checkbox>
                <el-checkbox label="sd">Stable Diffusion 风格</el-checkbox>
                <el-checkbox label="flux">FLUX 风格</el-checkbox>
              </el-checkbox-group>
            </el-form-item>

            <!-- Advanced Settings -->
            <el-collapse v-model="activeCollapse">
              <el-collapse-item title="高级设置" name="advanced">
                <el-form-item label="分析详细程度">
                  <el-slider
                    v-model="analysisLevel"
                    :min="1"
                    :max="5"
                    :marks="{ 1: '简单', 3: '标准', 5: '详细' }"
                    show-stops
                  />
                </el-form-item>

                <el-form-item label="包含风格描述">
                  <el-switch v-model="includeStyle" />
                </el-form-item>

                <el-form-item label="包含技术参数">
                  <el-switch v-model="includeTechnical" />
                </el-form-item>
              </el-collapse-item>
            </el-collapse>
          </div>

          <!-- Generate Button -->
          <div class="generate-section">
            <el-button
              type="primary"
              size="large"
              :loading="isLoading"
              :disabled="!canGenerate"
              @click="generatePrompt"
              style="width: 100%"
            >
              <template #loading>
                <el-icon class="is-loading"><Loading /></el-icon>
              </template>
              <el-icon v-if="!isLoading"><MagicStick /></el-icon>
              {{ isLoading ? 'AI 正在分析图片...' : '开始生成提示词' }}
            </el-button>
          </div>
        </el-card>
      </el-col>

      <!-- Right Panel - Results -->
      <el-col :span="12">
        <el-card class="results-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span class="card-title">生成结果</span>
              <el-button
                v-if="results.length > 0"
                type="primary"
                size="small"
                @click="exportResults"
              >
                <el-icon><Download /></el-icon>
                导出结果
              </el-button>
            </div>
          </template>

          <!-- Loading State -->
          <div v-if="isLoading" class="loading-container">
            <el-result icon="loading" title="AI 正在分析图片">
              <template #sub-title>
                <p>正在使用 {{ selectedModel }} 模型分析图片内容...</p>
                <el-progress :percentage="progress" :show-text="false" class="progress-bar" />
              </template>
            </el-result>
          </div>

          <!-- Error State -->
          <el-alert
            v-else-if="error"
            :title="error"
            type="error"
            :closable="false"
            show-icon
          />

          <!-- Results -->
          <div v-else-if="results.length > 0" class="results-container">
            <el-tabs v-model="activeResultTab" type="border-card">
              <el-tab-pane
                v-for="(result, index) in results"
                :key="index"
                :label="result.type"
                :name="result.type"
              >
                <div class="result-content">
                  <div class="result-header">
                    <el-tag :type="getResultTagType(result.type)" size="small">
                      {{ result.type }}
                    </el-tag>
                    <div class="result-actions">
                      <el-button
                        type="primary"
                        size="small"
                        @click="copyToClipboard(result.content)"
                      >
                        <el-icon><CopyDocument /></el-icon>
                        复制
                      </el-button>
                      <el-button
                        type="success"
                        size="small"
                        @click="saveToExamples(result)"
                      >
                        <el-icon><Collection /></el-icon>
                        保存
                      </el-button>
                    </div>
                  </div>

                  <div class="result-text">
                    <el-input
                      v-model="result.content"
                      type="textarea"
                      :rows="8"
                      readonly
                      resize="none"
                    />
                  </div>

                  <div class="result-stats">
                    <el-descriptions :column="3" size="small">
                      <el-descriptions-item label="字符数">
                        {{ result.content.length }}
                      </el-descriptions-item>
                      <el-descriptions-item label="单词数">
                        {{ result.content.split(' ').length }}
                      </el-descriptions-item>
                      <el-descriptions-item label="生成时间">
                        {{ formatRelativeTime(result.createdAt || new Date().toISOString()) }}
                      </el-descriptions-item>
                    </el-descriptions>
                  </div>
                </div>
              </el-tab-pane>
            </el-tabs>
          </div>

          <!-- Empty State -->
          <el-empty v-else description="上传图片并点击生成按钮开始分析">
            <el-button type="primary" @click="triggerFileInput">
              <el-icon><UploadFilled /></el-icon>
              选择图片
            </el-button>
          </el-empty>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useUIStore } from '@/stores/ui'
import { copyToClipboard as copyText } from '@/utils'

const uiStore = useUIStore()

const fileInput = ref<HTMLInputElement>()
const selectedImage = ref<string>('')
const selectedModel = ref('')
const promptTypes = ref(['general', 'mj', 'sd'])
const isLoading = ref(false)
const error = ref('')
const results = ref<Array<{ type: string; content: string }>>([])

const canGenerate = computed(() => {
  return selectedImage.value && selectedModel.value && promptTypes.value.length > 0
})

const triggerFileInput = () => {
  fileInput.value?.click()
}

const handleFileSelect = (event: Event) => {
  const file = (event.target as HTMLInputElement).files?.[0]
  if (file) {
    processFile(file)
  }
}

const handleDrop = (event: DragEvent) => {
  event.preventDefault()
  const file = event.dataTransfer?.files[0]
  if (file && file.type.startsWith('image/')) {
    processFile(file)
  }
}

const processFile = (file: File) => {
  const reader = new FileReader()
  reader.onload = (e) => {
    selectedImage.value = e.target?.result as string
  }
  reader.readAsDataURL(file)
}

const removeImage = () => {
  selectedImage.value = ''
  if (fileInput.value) {
    fileInput.value.value = ''
  }
}

const generatePrompt = async () => {
  if (!canGenerate.value) return

  isLoading.value = true
  error.value = ''
  results.value = []

  try {
    // TODO: Implement actual API call
    await new Promise(resolve => setTimeout(resolve, 2000)) // Simulate API call
    
    // Mock results
    results.value = [
      {
        type: '通用详细描述',
        content: '一张展示现代建筑设计的照片，建筑物采用玻璃幕墙结构，在蓝天白云的背景下显得格外醒目。建筑的几何线条简洁明快，体现了当代建筑的美学特征。'
      },
      {
        type: 'Midjourney 风格',
        content: 'modern glass building architecture, clean geometric lines, blue sky background, contemporary design, professional photography, architectural photography --ar 16:9 --v 6'
      },
      {
        type: 'Stable Diffusion 风格',
        content: 'modern glass building, architectural photography, clean lines, geometric design, blue sky, contemporary architecture, high quality, detailed, professional lighting'
      }
    ]
  } catch (err) {
    error.value = '生成失败，请稍后重试或检查图片。'
  } finally {
    isLoading.value = false
  }
}

const copyToClipboard = async (text: string) => {
  const success = await copyText(text)
  if (success) {
    uiStore.showToast('已复制到剪贴板', 'success')
  } else {
    uiStore.showToast('复制失败', 'error')
  }
}

// Handle paste events
const handlePaste = (event: ClipboardEvent) => {
  const items = event.clipboardData?.items
  if (items) {
    for (let i = 0; i < items.length; i++) {
      if (items[i].type.indexOf('image') !== -1) {
        const file = items[i].getAsFile()
        if (file) {
          processFile(file)
        }
        break
      }
    }
  }
}

onMounted(() => {
  document.addEventListener('paste', handlePaste)
})

onUnmounted(() => {
  document.removeEventListener('paste', handlePaste)
})
</script>

<style scoped>
.reverse-prompt {
  padding: 20px;
}

.upload-card,
.results-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 1.2rem;
  font-weight: bold;
  color: #e4e7ed;
}

.upload-section {
  margin-bottom: 20px;
}

.image-uploader {
  width: 100%;
}

.upload-placeholder {
  padding: 40px 20px;
  text-align: center;
  border: 2px dashed #409eff;
  border-radius: 8px;
  transition: border-color 0.3s;
}

.upload-placeholder:hover {
  border-color: #66b1ff;
}

.upload-icon {
  font-size: 48px;
  color: #409eff;
  margin-bottom: 16px;
}

.upload-text {
  font-size: 16px;
  color: #e4e7ed;
  margin-bottom: 8px;
}

.upload-hint {
  font-size: 12px;
  color: #909399;
  margin-bottom: 4px;
}

.uploaded-image-container {
  text-align: center;
}

.uploaded-image {
  max-width: 100%;
  max-height: 300px;
  border-radius: 8px;
  margin-bottom: 16px;
}

.image-actions {
  display: flex;
  justify-content: center;
}

.settings-section {
  margin-bottom: 20px;
}

.generate-section {
  margin-top: 20px;
}

.loading-container {
  padding: 40px 20px;
  text-align: center;
}

.progress-bar {
  margin-top: 16px;
  max-width: 300px;
  margin-left: auto;
  margin-right: auto;
}

.results-container {
  min-height: 400px;
}

.result-content {
  padding: 16px 0;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.result-actions {
  display: flex;
  gap: 8px;
}

.result-text {
  margin-bottom: 16px;
}

.result-stats {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding-top: 16px;
}

/* Element Plus dark theme customization */
:deep(.el-card) {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

:deep(.el-form-item__label) {
  color: #e4e7ed !important;
}

:deep(.el-input__wrapper) {
  background-color: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

:deep(.el-textarea__inner) {
  background-color: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: #e4e7ed;
}

:deep(.el-select .el-input__wrapper) {
  background-color: rgba(255, 255, 255, 0.1);
}

:deep(.el-checkbox__label) {
  color: #e4e7ed;
}

:deep(.el-tabs__item) {
  color: #e4e7ed;
}

:deep(.el-tabs__item.is-active) {
  color: #409eff;
}

:deep(.el-collapse-item__header) {
  color: #e4e7ed;
  background-color: rgba(255, 255, 255, 0.05);
}

:deep(.el-collapse-item__content) {
  background-color: rgba(255, 255, 255, 0.05);
}

:deep(.el-slider__runway) {
  background-color: rgba(255, 255, 255, 0.2);
}

:deep(.el-descriptions__label) {
  color: #e4e7ed !important;
}

:deep(.el-descriptions__content) {
  color: #e4e7ed !important;
}
</style>
