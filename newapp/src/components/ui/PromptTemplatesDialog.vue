<template>
  <el-dialog
    v-model="dialogVisible"
    title="提示词模板库"
    width="70%"
    :before-close="handleClose"
    class="templates-dialog"
  >
    <!-- Search and Filter -->
    <div class="search-section">
      <el-row :gutter="16">
        <el-col :span="16">
          <el-input
            v-model="searchQuery"
            placeholder="搜索模板..."
            clearable
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </el-col>
        <el-col :span="8">
          <el-select
            v-model="selectedCategory"
            placeholder="选择分类"
            clearable
            style="width: 100%"
          >
            <el-option
              v-for="category in categories"
              :key="category.value"
              :label="category.label"
              :value="category.value"
            />
          </el-select>
        </el-col>
      </el-row>
    </div>

    <!-- Templates Grid -->
    <div class="templates-grid">
      <div
        v-for="template in filteredTemplates"
        :key="template.id"
        class="template-card"
        @click="selectTemplate(template)"
      >
        <div class="template-header">
          <h4 class="template-title">{{ template.title }}</h4>
          <el-tag :type="getCategoryTagType(template.category)" size="small">
            {{ getCategoryLabel(template.category) }}
          </el-tag>
        </div>
        
        <div class="template-preview">
          <el-image
            v-if="template.preview"
            :src="template.preview"
            fit="cover"
            class="preview-image"
          >
            <template #error>
              <div class="image-placeholder">
                <el-icon><Picture /></el-icon>
              </div>
            </template>
          </el-image>
          <div v-else class="image-placeholder">
            <el-icon><Picture /></el-icon>
          </div>
        </div>

        <div class="template-content">
          <p class="template-prompt">{{ template.prompt }}</p>
          <div class="template-tags">
            <el-tag
              v-for="tag in template.tags.slice(0, 3)"
              :key="tag"
              size="small"
              class="tag-item"
            >
              {{ tag }}
            </el-tag>
            <el-tag
              v-if="template.tags.length > 3"
              size="small"
              type="info"
              class="tag-item"
            >
              +{{ template.tags.length - 3 }}
            </el-tag>
          </div>
        </div>

        <div class="template-actions">
          <el-button size="small" type="primary" @click.stop="selectTemplate(template)">
            使用模板
          </el-button>
          <el-button size="small" @click.stop="previewTemplate(template)">
            预览
          </el-button>
        </div>
      </div>
    </div>

    <!-- Empty State -->
    <el-empty v-if="filteredTemplates.length === 0" description="没有找到匹配的模板" />

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="createCustomTemplate">
          <el-icon><Plus /></el-icon>
          创建自定义模板
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Search, Picture, Plus } from '@element-plus/icons-vue'

interface Props {
  modelValue: boolean
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  select: [template: any]
}>()

const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const searchQuery = ref('')
const selectedCategory = ref('')

const categories = [
  { label: '人物肖像', value: 'portrait' },
  { label: '风景摄影', value: 'landscape' },
  { label: '艺术绘画', value: 'art' },
  { label: '动漫风格', value: 'anime' },
  { label: '概念设计', value: 'concept' },
  { label: '建筑设计', value: 'architecture' },
  { label: '产品设计', value: 'product' },
  { label: '抽象艺术', value: 'abstract' }
]

const templates = ref([
  {
    id: 1,
    title: '专业人物肖像',
    category: 'portrait',
    prompt: 'professional portrait photography, beautiful woman, soft lighting, shallow depth of field, elegant pose, natural makeup, studio lighting, high resolution, photorealistic',
    negativePrompt: 'blurry, low quality, distorted, ugly, bad anatomy',
    tags: ['肖像', '专业', '摄影', '美女'],
    preview: 'https://picsum.photos/300/200?random=1',
    settings: {
      model: 'flux-pro',
      aspectRatio: '3:4',
      guidance: 7.5,
      steps: 25
    }
  },
  {
    id: 2,
    title: '梦幻风景',
    category: 'landscape',
    prompt: 'magical fantasy landscape, floating islands, ethereal lighting, mystical atmosphere, vibrant colors, detailed environment, cinematic composition',
    negativePrompt: 'dark, gloomy, low quality, blurry',
    tags: ['风景', '梦幻', '魔法', '浮岛'],
    preview: 'https://picsum.photos/300/200?random=2',
    settings: {
      model: 'flux-pro',
      aspectRatio: '16:9',
      guidance: 8.0,
      steps: 30
    }
  },
  {
    id: 3,
    title: '动漫角色',
    category: 'anime',
    prompt: 'anime style character, beautiful girl, colorful hair, expressive eyes, detailed clothing, manga art style, vibrant colors, high quality illustration',
    negativePrompt: 'realistic, photographic, 3d render, low quality',
    tags: ['动漫', '角色', '美少女', '插画'],
    preview: 'https://picsum.photos/300/200?random=3',
    settings: {
      model: 'sdxl',
      aspectRatio: '1:1',
      guidance: 9.0,
      steps: 35
    }
  },
  {
    id: 4,
    title: '现代建筑',
    category: 'architecture',
    prompt: 'modern architecture, glass building, minimalist design, clean lines, urban environment, professional architectural photography, golden hour lighting',
    negativePrompt: 'old, damaged, low quality, blurry',
    tags: ['建筑', '现代', '玻璃', '极简'],
    preview: 'https://picsum.photos/300/200?random=4',
    settings: {
      model: 'flux-pro',
      aspectRatio: '16:9',
      guidance: 7.0,
      steps: 25
    }
  },
  {
    id: 5,
    title: '抽象艺术',
    category: 'abstract',
    prompt: 'abstract art, flowing colors, geometric shapes, modern composition, vibrant palette, digital art, contemporary style, artistic expression',
    negativePrompt: 'realistic, photographic, low quality',
    tags: ['抽象', '艺术', '几何', '现代'],
    preview: 'https://picsum.photos/300/200?random=5',
    settings: {
      model: 'sd3',
      aspectRatio: '1:1',
      guidance: 8.5,
      steps: 30
    }
  }
])

const filteredTemplates = computed(() => {
  let filtered = templates.value

  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(template =>
      template.title.toLowerCase().includes(query) ||
      template.prompt.toLowerCase().includes(query) ||
      template.tags.some(tag => tag.toLowerCase().includes(query))
    )
  }

  if (selectedCategory.value) {
    filtered = filtered.filter(template => template.category === selectedCategory.value)
  }

  return filtered
})

const getCategoryTagType = (category: string) => {
  const typeMap: Record<string, string> = {
    portrait: 'primary',
    landscape: 'success',
    art: 'warning',
    anime: 'danger',
    concept: 'info',
    architecture: 'primary',
    product: 'success',
    abstract: 'warning'
  }
  return typeMap[category] || 'default'
}

const getCategoryLabel = (category: string) => {
  const categoryItem = categories.find(c => c.value === category)
  return categoryItem?.label || category
}

const selectTemplate = (template: any) => {
  emit('select', template)
  ElMessage.success(`已选择模板: ${template.title}`)
}

const previewTemplate = (template: any) => {
  // TODO: Implement template preview
  ElMessage.info('预览功能开发中...')
}

const createCustomTemplate = () => {
  // TODO: Implement custom template creation
  ElMessage.info('自定义模板功能开发中...')
}

const handleClose = () => {
  emit('update:modelValue', false)
}
</script>

<style scoped>
.search-section {
  margin-bottom: 20px;
}

.templates-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
  max-height: 500px;
  overflow-y: auto;
}

.template-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.template-card:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: #409eff;
  transform: translateY(-2px);
}

.template-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.template-title {
  font-size: 16px;
  font-weight: 600;
  color: #e4e7ed;
  margin: 0;
}

.template-preview {
  width: 100%;
  height: 120px;
  margin-bottom: 12px;
  border-radius: 6px;
  overflow: hidden;
}

.preview-image {
  width: 100%;
  height: 100%;
}

.image-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.1);
  color: #909399;
  font-size: 24px;
}

.template-content {
  margin-bottom: 12px;
}

.template-prompt {
  font-size: 14px;
  color: #909399;
  line-height: 1.4;
  margin-bottom: 8px;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.template-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.tag-item {
  font-size: 12px;
}

.template-actions {
  display: flex;
  gap: 8px;
}

.dialog-footer {
  text-align: right;
}

/* Element Plus dark theme customization */
:deep(.el-dialog) {
  background: rgba(30, 30, 46, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

:deep(.el-dialog__header) {
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

:deep(.el-dialog__title) {
  color: #e4e7ed;
}

:deep(.el-input__wrapper) {
  background-color: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

:deep(.el-select .el-input__wrapper) {
  background-color: rgba(255, 255, 255, 0.1);
}
</style>
