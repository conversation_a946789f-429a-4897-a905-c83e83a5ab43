<template>
  <el-card class="example-card" shadow="hover" @click="$emit('view', example)">
    <!-- Image -->
    <div class="image-container">
      <el-image
        :src="example.image_url"
        :alt="example.title"
        fit="cover"
        class="example-image"
        @error="handleImageError"
      >
        <template #error>
          <div class="image-error">
            <el-icon><Picture /></el-icon>
            <span>加载失败</span>
          </div>
        </template>
      </el-image>

      <!-- Overlay Actions -->
      <div class="image-overlay">
        <div class="overlay-actions">
          <el-button
            circle
            type="primary"
            @click.stop="$emit('view', example)"
            title="查看详情"
          >
            <el-icon><View /></el-icon>
          </el-button>

          <el-button
            circle
            :type="example.is_liked ? 'danger' : 'default'"
            @click.stop="handleLike"
            title="点赞"
          >
            <el-icon><Star /></el-icon>
          </el-button>

          <el-button
            v-if="canDelete"
            circle
            type="danger"
            @click.stop="handleDelete"
            title="删除"
          >
            <el-icon><Delete /></el-icon>
          </el-button>
        </div>
      </div>

      <!-- Badges -->
      <div class="badges">
        <el-tag
          v-if="example.category"
          :type="getCategoryTagType(example.category.slug)"
          size="small"
          class="category-badge"
        >
          {{ example.category.name }}
        </el-tag>

        <el-tag
          :type="getModelTagType(example.model)"
          size="small"
          class="model-badge"
        >
          {{ example.model }}
        </el-tag>
      </div>
    </div>

    <!-- Content -->
    <template #default>
      <div class="card-content">
        <!-- Title -->
        <h3 class="example-title">
          {{ example.title }}
        </h3>

        <!-- Prompt -->
        <p class="example-prompt">
          {{ example.prompt }}
        </p>

        <!-- Tags -->
        <div v-if="example.tags && example.tags.length > 0" class="tags-container">
          <el-tag
            v-for="tag in example.tags.slice(0, 3)"
            :key="tag.id"
            size="small"
            class="tag-item"
          >
            {{ tag.name }}
          </el-tag>
          <el-tag
            v-if="example.tags.length > 3"
            size="small"
            type="info"
            class="tag-item"
          >
            +{{ example.tags.length - 3 }}
          </el-tag>
        </div>

        <!-- Footer -->
        <div class="card-footer">
          <div class="footer-info">
            <span class="time-info">{{ formatRelativeTime(example.created_at) }}</span>
            <span class="separator">•</span>
            <span class="size-info">{{ example.width }}×{{ example.height }}</span>
          </div>

          <div class="likes-info">
            <el-icon class="like-icon"><Star /></el-icon>
            <span>{{ example.likes_count || 0 }}</span>
          </div>
        </div>
      </div>
    </template>
  </el-card>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { formatRelativeTime } from '@/utils'
import { Picture, View, Star, Delete } from '@element-plus/icons-vue'
import type { Example } from '@/types'

interface Props {
  example: Example
}

const props = defineProps<Props>()

const emit = defineEmits<{
  like: [id: number]
  delete: [id: number]
  view: [example: Example]
}>()

const authStore = useAuthStore()

const canDelete = computed(() => {
  return authStore.isAdmin || authStore.user?.id === props.example.user_id
})

const getCategoryTagType = (categorySlug: string) => {
  const typeMap: Record<string, string> = {
    people: 'warning',
    landscape: 'success',
    animals: 'info',
    architecture: 'primary',
    art: 'danger'
  }
  return typeMap[categorySlug] || 'default'
}

const getModelTagType = (model: string) => {
  const typeMap: Record<string, string> = {
    'MJ': 'primary',
    'FLUX': 'success',
    'SDXL': 'warning',
    'GPT4O': 'danger'
  }
  return typeMap[model] || 'info'
}

const handleLike = () => {
  emit('like', props.example.id)
}

const handleDelete = () => {
  emit('delete', props.example.id)
}

const handleImageError = () => {
  console.log('Image load error')
}
</script>

<style scoped>
.example-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  cursor: pointer;
}

.example-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.image-container {
  position: relative;
  width: 100%;
  height: 200px;
  overflow: hidden;
  border-radius: 8px 8px 0 0;
}

.example-image {
  width: 100%;
  height: 100%;
}

.image-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #909399;
  background: rgba(255, 255, 255, 0.1);
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.image-container:hover .image-overlay {
  opacity: 1;
}

.overlay-actions {
  display: flex;
  gap: 10px;
}

.badges {
  position: absolute;
  top: 8px;
  left: 8px;
  right: 8px;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.category-badge {
  backdrop-filter: blur(10px);
}

.model-badge {
  backdrop-filter: blur(10px);
}

.card-content {
  padding: 16px;
}

.example-title {
  font-size: 1rem;
  font-weight: 600;
  color: #e4e7ed;
  margin-bottom: 8px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  line-height: 1.4;
}

.example-prompt {
  font-size: 0.875rem;
  color: #909399;
  margin-bottom: 12px;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  line-height: 1.4;
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  margin-bottom: 12px;
}

.tag-item {
  font-size: 0.75rem;
}

.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.75rem;
  color: #909399;
}

.footer-info {
  display: flex;
  align-items: center;
  gap: 4px;
}

.separator {
  margin: 0 4px;
}

.likes-info {
  display: flex;
  align-items: center;
  gap: 4px;
}

.like-icon {
  font-size: 0.875rem;
}

/* Element Plus customization */
:deep(.el-card__body) {
  padding: 0;
}

:deep(.el-image) {
  display: block;
}
</style>
