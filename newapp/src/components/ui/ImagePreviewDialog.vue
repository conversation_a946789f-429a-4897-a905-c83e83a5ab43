<template>
  <el-dialog
    v-model="dialogVisible"
    title="图片预览"
    width="80%"
    :before-close="handleClose"
    class="image-preview-dialog"
  >
    <div v-if="image" class="preview-container">
      <div class="image-section">
        <el-image
          :src="image.url"
          :alt="image.prompt"
          fit="contain"
          class="preview-image"
        >
          <template #error>
            <div class="image-error">
              <el-icon><Picture /></el-icon>
              <span>图片加载失败</span>
            </div>
          </template>
        </el-image>
      </div>

      <div class="info-section">
        <el-descriptions title="图片信息" :column="1" border>
          <el-descriptions-item label="提示词">
            <div class="prompt-text">{{ image.prompt }}</div>
          </el-descriptions-item>
          <el-descriptions-item label="生成模型">
            <el-tag :type="getModelTagType(image.model)">{{ image.model }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="生成时间">
            {{ formatTime(image.createdAt) }}
          </el-descriptions-item>
          <el-descriptions-item v-if="image.settings" label="参数设置">
            <div class="settings-info">
              <el-tag size="small" class="setting-tag">
                尺寸: {{ image.settings.aspectRatio }}
              </el-tag>
              <el-tag size="small" class="setting-tag">
                引导: {{ image.settings.guidance }}
              </el-tag>
              <el-tag size="small" class="setting-tag">
                步数: {{ image.settings.steps }}
              </el-tag>
              <el-tag v-if="image.settings.seed" size="small" class="setting-tag">
                种子: {{ image.settings.seed }}
              </el-tag>
            </div>
          </el-descriptions-item>
        </el-descriptions>

        <div class="action-buttons">
          <el-button type="primary" @click="handleDownload">
            <el-icon><Download /></el-icon>
            下载图片
          </el-button>
          <el-button type="success" @click="handleSave">
            <el-icon><Collection /></el-icon>
            保存到案例库
          </el-button>
          <el-button @click="copyPrompt">
            <el-icon><CopyDocument /></el-icon>
            复制提示词
          </el-button>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Picture, Download, Collection, CopyDocument } from '@element-plus/icons-vue'
import { copyToClipboard, formatRelativeTime } from '@/utils'

interface Props {
  modelValue: boolean
  image: any
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  save: [image: any]
  download: [image: any]
}>()

const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const getModelTagType = (model: string) => {
  const typeMap: Record<string, string> = {
    'flux-pro': 'primary',
    'flux-dev': 'success',
    'midjourney': 'warning',
    'sdxl': 'info',
    'sd3': 'danger'
  }
  return typeMap[model] || 'default'
}

const formatTime = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

const handleClose = () => {
  emit('update:modelValue', false)
}

const handleDownload = () => {
  emit('download', props.image)
}

const handleSave = () => {
  emit('save', props.image)
}

const copyPrompt = async () => {
  if (props.image?.prompt) {
    const success = await copyToClipboard(props.image.prompt)
    if (success) {
      ElMessage.success('提示词已复制到剪贴板')
    } else {
      ElMessage.error('复制失败')
    }
  }
}
</script>

<style scoped>
.preview-container {
  display: flex;
  gap: 20px;
  min-height: 500px;
}

.image-section {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  padding: 20px;
}

.preview-image {
  max-width: 100%;
  max-height: 500px;
  border-radius: 8px;
}

.image-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #909399;
  font-size: 16px;
}

.info-section {
  flex: 0 0 400px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.prompt-text {
  line-height: 1.6;
  color: #e4e7ed;
  background: rgba(255, 255, 255, 0.05);
  padding: 12px;
  border-radius: 6px;
  max-height: 120px;
  overflow-y: auto;
}

.settings-info {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.setting-tag {
  margin-bottom: 4px;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-top: auto;
}

.dialog-footer {
  text-align: right;
}

/* Element Plus dark theme customization */
:deep(.el-dialog) {
  background: rgba(30, 30, 46, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

:deep(.el-dialog__header) {
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

:deep(.el-dialog__title) {
  color: #e4e7ed;
}

:deep(.el-descriptions__label) {
  color: #e4e7ed !important;
}

:deep(.el-descriptions__content) {
  color: #e4e7ed !important;
}

:deep(.el-descriptions__title) {
  color: #e4e7ed !important;
}

@media (max-width: 768px) {
  .preview-container {
    flex-direction: column;
  }
  
  .info-section {
    flex: none;
  }
}
</style>
