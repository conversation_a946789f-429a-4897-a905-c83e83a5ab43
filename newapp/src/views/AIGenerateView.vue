<template>
  <div class="ai-generate-view">
    <!-- Header -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h1 class="page-title">AI 图片生成</h1>
          <p class="page-subtitle">使用先进的AI模型生成高质量图片</p>
        </div>
        <div class="header-right">
          <el-button @click="$router.go(-1)">
            <el-icon><ArrowLeft /></el-icon>
            返回
          </el-button>
        </div>
      </div>
    </div>

    <!-- Main Content -->
    <div class="page-content">
      <AIImageGeneration />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ArrowLeft } from '@element-plus/icons-vue'
import AIImageGeneration from '@/components/features/AIImageGeneration.vue'
</script>

<style scoped>
.ai-generate-view {
  min-height: 100vh;
  background: linear-gradient(135deg, #1e1e2e 0%, #2d2d44 100%);
}

.page-header {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  padding: 20px 0;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-title {
  font-size: 2rem;
  font-weight: bold;
  color: #e4e7ed;
  margin: 0 0 8px 0;
  background: linear-gradient(135deg, #409eff, #8b5cf6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.page-subtitle {
  color: #909399;
  margin: 0;
  font-size: 1rem;
}

.page-content {
  max-width: 1200px;
  margin: 0 auto;
}
</style>
